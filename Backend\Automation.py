# Safe print function for PyInstaller executables
def safe_print(message):
    """Print function that handles Unicode encoding issues in PyInstaller"""
    try:
        print(message)
    except UnicodeEncodeError:
        # Fallback: replace problematic characters
        safe_message = message.encode('ascii', 'replace').decode('ascii')
        print(safe_message)
    except Exception:
        # Ultimate fallback
        print("Warning: Module import issue detected")

# Graceful imports with fallbacks for PyInstaller compatibility
try:
    from AppOpener import close, open as appopen
    HAS_APPOPENER = True
except ImportError:
    safe_print("Warning: AppOpener not available - using fallback methods")
    HAS_APPOPENER = False
    appopen = None
    close = None

try:
    from pywhatkit import search, playonyt
    HAS_PYWHATKIT = True
except ImportError:
    safe_print("Warning: pywhatkit not available - using fallback methods")
    HAS_PYWHATKIT = False
    search = None
    playonyt = None

try:
    import keyboard
    HAS_KEYBOARD = True
except ImportError:
    safe_print("Warning: keyboard not available - system controls disabled")
    HAS_KEYBOARD = False
    keyboard = None

from webbrowser import open as webopen
from dotenv import dotenv_values
from bs4 import BeautifulSoup
from rich import print
from groq import Groq
import webbrowser
import subprocess
import requests
import asyncio
import os
import re
import urllib.parse

# Load environment variables
env_values = dotenv_values(".env")
GroqAPIKey = env_values["GroqAPIKey"]

# Email automation configuration
AUTO_SEND_EMAILS = env_values.get("AUTO_SEND_EMAILS", "True").lower() == "true"

# Some classes and responses for future use
classes = [
    "zCubwf", "hgKElc", "LTKOO SY7ric", "ZOLCW",
    "gsrt vk_bk FzvWSb YwPhnf", "pclqee",
    "tw-Data-text tw-text-small tw-ta", "IZ6rdc", "05uR6d LTK00",
    "vlzY6d", "webanswers-webanswers_table_webanswers-table",
    "dDoNo ikb4Bb gsrt", "sXLa0e", "LWkfKe", "VQF4g", "qv3Wpe",
    "kno-rdesc", "SPZz6b"
]

useragent = (
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) "
    "AppleWebKit/537.36 (KHTML, like Gecko) "
    "Chrome/100.0.4896.75 Safari/537.36"
)

client = Groq(api_key=GroqAPIKey)

professional_responses = [
    "Your satisfaction is my top priority; feel free to reach out if there's anything else I can help you with.",
    "I'm at your service for any additional questions or support you may need - don't hesitate to ask.",
]

messages = []

# Use os.getenv for a safe fallback if the username is not set
SystemChatBot = [{
    "role": "system",
    "content": f"Hello, I am {os.getenv('USERNAME', 'User')}! How can I help you today?"
}]

def GoogleSearch(Topic):
    if HAS_PYWHATKIT and search:
        search(Topic)
    else:
        # Fallback: open Google search in browser
        search_url = f"https://www.google.com/search?q={Topic}"
        webbrowser.open(search_url)
    return True

def ContentWriterAI(prompt):
    messages.append({"role": "user", "content": prompt})
    completion = client.chat.completions.create(
        model="mixtral-8x7b-32768",
        messages=SystemChatBot + messages,
        max_tokens=2028,
        temperature=0.7,
        top_p=1,
        stream=True,
        stop=None
    )
    Answer = ""
    for chunk in completion:
        if chunk.choices[0].delta.content:
            Answer += chunk.choices[0].delta.content
    Answer = Answer.replace("</s>: ", "")
    messages.append({"role": "assistant", "content": Answer})
    return Answer

def Content(topic):
    def OpenNotepad(file_path):
        default_text_editor = 'notepad.exe'
        # Prevent creating new console window
        subprocess.Popen([default_text_editor, file_path],
                        creationflags=subprocess.CREATE_NO_WINDOW if hasattr(subprocess, 'CREATE_NO_WINDOW') else 0)

    # Remove any "Content " prefix if present
    Topic_str = topic.replace("Content ", "")
    ContentByAI = ContentWriterAI(Topic_str)

    file_path = rf"Data\{Topic_str.lower().replace(' ', '')}.txt"
    with open(file_path, "w") as file:
        file.write(ContentByAI)

    OpenNotepad(file_path)
    return True

def YoutubeSearch(Topic):
    Url4Search = f"https://www.youtube.com/results?search_query={Topic}"
    webbrowser.open(Url4Search)
    return True

def PlayYoutube(query):
    if HAS_PYWHATKIT and playonyt:
        playonyt(query)
    else:
        # Fallback: open YouTube search in browser
        youtube_url = f"https://www.youtube.com/results?search_query={query}"
        webbrowser.open(youtube_url)
    return True

def OpenApp(app, sess=requests.session()):
    try:
        # Check if it's a direct URL (shouldn't happen here, but just in case)
        if app.startswith(('http://', 'https://')):
            success, website_name = OpenURL(app)
            return (success, website_name)

        # Try to open the app via AppOpener if available
        if HAS_APPOPENER and appopen:
            appopen(app, match_closest=True, output=True, throw_error=True)
            return (True, app.capitalize())  # Return app name for speech
        else:
            raise Exception("AppOpener not available, using fallback")
    except Exception as e:
        safe_print(f"Error opening app with AppOpener: {e}")

        # Fallback: use a mapping for known apps to their website URLs
        fallback_websites = {
            "facebook": "https://www.facebook.com",
            "twitter": "https://www.twitter.com",
            "instagram": "https://www.instagram.com",
            "youtube": "https://www.youtube.com",
            "gmail": "https://mail.google.com",
            "google": "https://www.google.com",
            "amazon": "https://www.amazon.com",
            "netflix": "https://www.netflix.com",
            "linkedin": "https://www.linkedin.com",
            "github": "https://github.com",
            # Add more mappings as needed.
        }
        if app.lower() in fallback_websites:
            webopen(fallback_websites[app.lower()])
            return (True, app.capitalize())  # Return app name for speech

        # If no mapping exists, perform a Google search as a fallback.
        def extract_links(html):
            if html is None:
                return []
            soup = BeautifulSoup(html, "html.parser")
            links = soup.find_all('a', {'jsname': 'UWckNb'})
            return [link['href'] for link in links if 'href' in link.attrs]

        def search_google(query):
            url = f"https://www.google.com/search?q={query}"
            headers = {'User-Agent': useragent}
            response = sess.get(url, headers=headers)
            if response.status_code == 200:
                return response.text
            else:
                safe_print("Failed to retrieve search results.")
            return None

        html = search_google(app)
        if html:
            links = extract_links(html)
            if links:
                link = links[0]
                webopen(link)
                return (True, app.capitalize())  # Return app name for speech
            else:
                safe_print("No links found in search results.")
        else:
            safe_print("Google search failed.")
        return (True, app.capitalize())  # Return app name for speech even if we failed

def OpenURL(url):
    """
    Open a direct URL in the default web browser.

    Args:
        url (str): The URL to open. Should start with http:// or https://

    Returns:
        tuple: (bool, str) - Success status and website name for speech
    """
    try:
        # Validate URL format
        if not url.startswith(('http://', 'https://')):
            safe_print(f"Invalid URL format: {url}")
            safe_print("URL must start with http:// or https://")
            return False, "invalid website"

        # Extract website name for speech
        from urllib.parse import urlparse

        # Parse the URL
        parsed_url = urlparse(url)
        domain = parsed_url.netloc

        # Remove www. prefix if present
        if domain.startswith('www.'):
            domain = domain[4:]

        # Extract the base domain (e.g., 'example.com' from 'example.com/page')
        base_domain = domain.split(':')[0]  # Remove port if present

        # Extract the main name (e.g., 'example' from 'example.com')
        website_name = base_domain.split('.')[0]

        # Make it more readable for speech
        website_name = website_name.replace('-', ' ').replace('_', ' ')
        website_name = ' '.join(word.capitalize() for word in website_name.split())

        # Try to open the URL in the default browser
        safe_print(f"Opening URL: {url}")
        safe_print(f"Website name for speech: {website_name}")
        webbrowser.open(url)
        return True, website_name
    except Exception as e:
        safe_print(f"Error opening URL: {e}")
        return False, "website"

def CloseApp(app):
    if "chrome" in app.lower():
        # Additional logic for chrome can be added here if needed
        pass
    else:
        try:
            if HAS_APPOPENER and close:
                close(app, match_closest=True, output=True, throw_error=True)
                return True
            else:
                safe_print(f"AppOpener not available - cannot close {app}")
                return False
        except Exception as e:
            safe_print(f"Error closing app: {e}")
            return False

def validate_email(email):
    """
    Validate email address format using regex.

    Args:
        email (str): Email address to validate

    Returns:
        bool: True if email format is valid, False otherwise
    """
    email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(email_pattern, email.strip()) is not None

def extract_email_from_text(text):
    """
    Extract email address from text using regex.

    Args:
        text (str): Text that may contain an email address

    Returns:
        str or None: Extracted email address if found, None otherwise
    """
    email_pattern = r'\b[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}\b'
    matches = re.findall(email_pattern, text)
    return matches[0] if matches else None

def _get_voice_confirmation(email_address, subject, body):
    """
    Get voice confirmation from user before sending email.

    Args:
        email_address (str): Recipient email address
        subject (str): Email subject
        body (str): Email body content

    Returns:
        bool: True if user confirms, False otherwise
    """
    try:
        # Import required modules for voice interaction
        from Backend.SpeechToText import SpeechRecognition
        from Backend.TextToSpeech import text_to_speech
        from Frontend.GUI import SetAssistantStatus, ShowTextToScreen
        from dotenv import dotenv_values

        # Get assistant name from environment
        env_values = dotenv_values(".env")
        assistant_name = env_values.get("Assistantname", "Matrix")

        # Create confirmation message
        confirmation_message = f"I have prepared an email to {email_address}"
        if subject:
            confirmation_message += f" with subject '{subject}'"
        if body:
            # Show first 50 characters of body
            body_preview = body[:50] + "..." if len(body) > 50 else body
            confirmation_message += f" and content '{body_preview}'"

        confirmation_message += ". The Gmail compose window is now open for your review. Would you like me to send this email automatically? Please say yes to confirm or no to cancel."

        safe_print(f"Asking for confirmation: {confirmation_message}")

        # Display message and speak it
        ShowTextToScreen(f"{assistant_name} : {confirmation_message}")
        SetAssistantStatus("Asking for confirmation...")
        text_to_speech(confirmation_message)

        # Add a small delay to ensure TTS completes before listening
        import time
        time.sleep(1)

        # Listen for user response
        SetAssistantStatus("Listening for confirmation...")
        safe_print("Waiting for user voice confirmation...")

        user_response = SpeechRecognition()

        # Check if speech recognition failed
        if user_response in ["I didn't hear anything. Please try again.",
                           "I'm having trouble with speech recognition. Please try again later.",
                           "I didn't catch that.",
                           "Error in speech recognition"]:
            safe_print(f"Speech recognition failed: {user_response}")

            # Try one more time
            retry_message = "I didn't catch that. Please say yes to send the email or no to cancel."
            ShowTextToScreen(f"{assistant_name} : {retry_message}")
            text_to_speech(retry_message)

            SetAssistantStatus("Listening for confirmation...")
            user_response = SpeechRecognition()

            if user_response in ["I didn't hear anything. Please try again.",
                               "I'm having trouble with speech recognition. Please try again later.",
                               "I didn't catch that.",
                               "Error in speech recognition"]:
                safe_print("Speech recognition failed again, defaulting to manual sending")
                return False

        # Display user response
        ShowTextToScreen(f"User : {user_response}")

        # Process the response - remove punctuation and convert to lowercase
        user_response_clean = user_response.lower().strip()
        # Remove common punctuation that QueryModifier adds
        user_response_clean = user_response_clean.replace('.', '').replace('?', '').replace('!', '').replace(',', '')
        safe_print(f"User response (original): {user_response}")
        safe_print(f"User response (cleaned): {user_response_clean}")

        # Check for positive confirmation - use more flexible matching
        positive_words = ["yes", "yeah", "yep", "sure", "okay", "ok", "send", "confirm", "go ahead", "proceed", "affirmative"]
        negative_words = ["no", "nope", "cancel", "stop", "don't", "abort", "decline", "negative"]

        # Check for positive confirmation - look for any positive word in the response
        is_positive = False
        for word in positive_words:
            if word in user_response_clean:
                is_positive = True
                safe_print(f"Found positive word: '{word}' in response")
                break

        # Check for negative response - look for any negative word in the response
        is_negative = False
        for word in negative_words:
            if word in user_response_clean:
                is_negative = True
                safe_print(f"Found negative word: '{word}' in response")
                break

        if is_positive and not is_negative:
            safe_print("User confirmed email sending")
            confirmation_response = "Confirmed! I will send the email now."
            ShowTextToScreen(f"{assistant_name} : {confirmation_response}")
            text_to_speech(confirmation_response)
            return True

        elif is_negative:
            safe_print("User declined email sending")
            decline_response = "Understood. The email will not be sent automatically. You can review and send it manually from the Gmail window."
            ShowTextToScreen(f"{assistant_name} : {decline_response}")
            text_to_speech(decline_response)
            return False

        # Ambiguous response
        else:
            safe_print(f"Ambiguous response: {user_response}")
            clarify_message = "I'm not sure if you want to send the email. Please say yes to send or no to cancel."
            ShowTextToScreen(f"{assistant_name} : {clarify_message}")
            text_to_speech(clarify_message)

            # One more attempt
            SetAssistantStatus("Listening for confirmation...")
            final_response = SpeechRecognition()

            if final_response not in ["I didn't hear anything. Please try again.",
                                    "I'm having trouble with speech recognition. Please try again later.",
                                    "I didn't catch that.",
                                    "Error in speech recognition"]:
                ShowTextToScreen(f"User : {final_response}")

                # Clean the final response the same way
                final_response_clean = final_response.lower().strip()
                final_response_clean = final_response_clean.replace('.', '').replace('?', '').replace('!', '').replace(',', '')
                safe_print(f"Final response (cleaned): {final_response_clean}")

                # Check for positive confirmation in final response
                final_is_positive = False
                for word in positive_words:
                    if word in final_response_clean:
                        final_is_positive = True
                        safe_print(f"Found positive word in final response: '{word}'")
                        break

                if final_is_positive:
                    safe_print("User confirmed email sending (final attempt)")
                    confirmation_response = "Confirmed! I will send the email now."
                    ShowTextToScreen(f"{assistant_name} : {confirmation_response}")
                    text_to_speech(confirmation_response)
                    return True

            # Default to manual sending if still unclear
            safe_print("Could not get clear confirmation, defaulting to manual sending")
            default_response = "I'll leave the email open for you to review and send manually."
            ShowTextToScreen(f"{assistant_name} : {default_response}")
            text_to_speech(default_response)
            return False

    except Exception as e:
        safe_print(f"Error getting voice confirmation: {e}")
        return False

def _automate_gmail_send_complete(email_address, subject, body):
    """
    Complete email automation: navigate to Gmail, compose, and send email.

    Args:
        email_address (str): Recipient email address
        subject (str): Email subject
        body (str): Email body content

    Returns:
        bool: True if email was sent successfully, False otherwise
    """
    try:
        # Import required modules
        from selenium import webdriver
        from selenium.webdriver.common.by import By
        from selenium.webdriver.chrome.service import Service
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.support.ui import WebDriverWait
        from selenium.webdriver.support import expected_conditions as EC
        from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException
        from webdriver_manager.chrome import ChromeDriverManager
        import time

        safe_print("Attempting to automate Gmail send...")

        # Create a separate WebDriver instance for email automation
        chrome_options = Options()
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")
        # Don't use headless mode so user can see what's happening

        try:
            service = Service(ChromeDriverManager().install())
            driver = webdriver.Chrome(service=service, options=chrome_options)
            safe_print("Created dedicated WebDriver for email automation")
        except Exception as e:
            safe_print(f"Failed to create WebDriver: {e}")
            return False

        # Set a timeout for the entire automation process
        automation_start_time = time.time()
        automation_timeout = 60  # 60 seconds total timeout

        safe_print("Starting Gmail automation with 60-second timeout...")

        # Navigate to Gmail compose URL directly
        try:
            # Create Gmail compose URL with pre-filled data
            encoded_email = urllib.parse.quote(email_address)
            encoded_subject = urllib.parse.quote(subject) if subject else ""
            encoded_body = urllib.parse.quote(body) if body else ""

            gmail_compose_url = f"https://mail.google.com/mail/?view=cm&fs=1&to={encoded_email}"
            if encoded_subject:
                gmail_compose_url += f"&su={encoded_subject}"
            if encoded_body:
                gmail_compose_url += f"&body={encoded_body}"

            safe_print(f"Navigating to Gmail compose for: {email_address}")
            driver.get(gmail_compose_url)

            # Wait for the page to load
            WebDriverWait(driver, 20).until(
                lambda d: d.execute_script("return document.readyState") == "complete"
            )

            # Wait for Gmail to fully initialize
            time.sleep(10)  # Give more time for Gmail to load completely

            # Check if we need to sign in
            if "accounts.google.com" in driver.current_url or "signin" in driver.current_url.lower():
                safe_print("Gmail requires sign-in. Please sign in manually and try again.")
                driver.quit()
                return False

            # Check if compose window is loaded
            if "compose" not in driver.current_url.lower() and "view=cm" not in driver.current_url:
                safe_print("Compose window not detected. Trying keyboard shortcut anyway...")
            else:
                safe_print("✅ Compose window detected, proceeding with automation...")

            # Try keyboard shortcut first (most reliable method)
            safe_print("🎯 Attempting direct keyboard shortcut (Ctrl+Enter)...")
            try:
                from selenium.webdriver.common.keys import Keys
                from selenium.webdriver.common.action_chains import ActionChains

                # Wait a bit more for compose to be ready
                time.sleep(3)

                # Send Ctrl+Enter directly
                ActionChains(driver).send_keys(Keys.CONTROL + Keys.ENTER).perform()
                time.sleep(4)

                # Check if we're no longer in compose mode (email sent)
                current_url = driver.current_url
                if "compose" not in current_url.lower() and "view=cm" not in current_url:
                    safe_print("✅ Email sent successfully via keyboard shortcut!")
                    driver.quit()
                    return True
                else:
                    safe_print("⚠️ Still in compose mode, trying button detection...")

            except Exception as e:
                safe_print(f"Keyboard shortcut attempt failed: {e}")
                safe_print("Falling back to button detection...")

        except Exception as e:
            safe_print(f"Error navigating to Gmail: {e}")
            try:
                driver.quit()
            except:
                pass
            return False

        # Try multiple selectors for the Send button (Gmail interface variations)
        send_button_selectors = [
            # Modern Gmail interface
            "div[data-tooltip='Send ⌘+Enter']",
            "div[aria-label='Send ⌘+Enter']",
            "div[data-tooltip='Send']",
            "div[aria-label='Send']",
            # Alternative selectors
            "div[role='button'][aria-label*='Send']",
            "div[role='button'][data-tooltip*='Send']",
            # Older Gmail interface
            "div.T-I.J-J5-Ji.aoO.v7.T-I-atl.L3",
            "div.T-I.J-J5-Ji.aoO.T-I-atl.L3",
            # Text-based fallback
            "div[role='button']:contains('Send')",
            # Tab key navigation fallback
            "div.btC div[role='button']"
        ]

        send_button = None
        wait = WebDriverWait(driver, 15)

        # Check timeout before starting send button search
        if time.time() - automation_start_time > automation_timeout:
            safe_print("Automation timeout reached before send button search")
            driver.quit()
            return False

        # Try each selector with shorter timeouts
        for selector in send_button_selectors:
            try:
                # Check timeout for each attempt
                if time.time() - automation_start_time > automation_timeout:
                    safe_print("Automation timeout reached during send button search")
                    driver.quit()
                    return False

                safe_print(f"Trying selector: {selector}")
                if ":contains" in selector:
                    # Use JavaScript for text-based selection
                    send_button = driver.execute_script("""
                        var elements = document.querySelectorAll('div[role="button"]');
                        for (var i = 0; i < elements.length; i++) {
                            if (elements[i].textContent.includes('Send')) {
                                return elements[i];
                            }
                        }
                        return null;
                    """)
                else:
                    # Use shorter timeout for individual selectors
                    short_wait = WebDriverWait(driver, 3)
                    send_button = short_wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, selector)))

                if send_button:
                    safe_print(f"Found Send button with selector: {selector}")
                    break

            except (TimeoutException, NoSuchElementException):
                continue

        # If no button found with CSS selectors, try XPath
        if not send_button:
            xpath_selectors = [
                "//div[@role='button' and contains(@aria-label, 'Send')]",
                "//div[@role='button' and contains(@data-tooltip, 'Send')]",
                "//div[@role='button' and contains(text(), 'Send')]",
                "//div[contains(@class, 'T-I') and contains(@class, 'J-J5-Ji')]",
                "//div[text()='Send']",
                "//button[text()='Send']"
            ]

            for xpath in xpath_selectors:
                try:
                    safe_print(f"Trying XPath: {xpath}")
                    send_button = wait.until(EC.element_to_be_clickable((By.XPATH, xpath)))
                    if send_button:
                        safe_print(f"Found Send button with XPath: {xpath}")
                        break
                except (TimeoutException, NoSuchElementException):
                    continue

        # If still no button found, try keyboard shortcut (most reliable method)
        if not send_button:
            safe_print("Send button not found, trying keyboard shortcut Ctrl+Enter...")
            try:
                from selenium.webdriver.common.keys import Keys
                from selenium.webdriver.common.action_chains import ActionChains

                # Focus on the body and send with Ctrl+Enter
                body_element = driver.find_element(By.TAG_NAME, "body")
                body_element.click()
                time.sleep(1)

                # Send Ctrl+Enter
                ActionChains(driver).send_keys(Keys.CONTROL + Keys.ENTER).perform()
                time.sleep(3)

                safe_print("Keyboard shortcut sent successfully")
                driver.quit()
                return True

            except Exception as e:
                safe_print(f"Keyboard shortcut failed: {e}")
                try:
                    driver.quit()
                except:
                    pass
                return False

        # Click the Send button
        if send_button:
            try:
                # Scroll to the button if needed
                driver.execute_script("arguments[0].scrollIntoView(true);", send_button)
                time.sleep(1)

                # Try clicking the button
                send_button.click()
                safe_print("Send button clicked successfully!")

                # Wait a moment to ensure the email is sent
                time.sleep(3)

                # Check if email was sent (look for confirmation or compose window closing)
                try:
                    # Look for sent confirmation or compose window disappearing
                    WebDriverWait(driver, 5).until(
                        lambda d: len(d.find_elements(By.CSS_SELECTOR, "div[data-tooltip='Send']")) == 0
                    )
                    safe_print("Email sent successfully!")
                    driver.quit()
                    return True
                except TimeoutException:
                    # Compose window still there, but click was registered
                    safe_print("Send button clicked, email should be sending...")
                    driver.quit()
                    return True

            except Exception as e:
                safe_print(f"Error clicking Send button: {e}")
                try:
                    driver.quit()
                except:
                    pass
                return False

        safe_print("Could not find or click Send button")
        try:
            driver.quit()
        except:
            pass
        return False

    except Exception as e:
        safe_print(f"Error in Gmail automation: {e}")
        # Make sure to clean up WebDriver even if there's an error
        try:
            if 'driver' in locals():
                driver.quit()
        except:
            pass
        return False

def SendEmail(email_address, subject="", body=""):
    """
    Open Gmail compose window with pre-filled recipient, subject, and body,
    then automatically send the email using browser automation.

    Args:
        email_address (str): The email address to send to
        subject (str): Email subject/topic
        body (str): Email content/body

    Returns:
        tuple: (bool, str) - Success status and message for speech feedback
    """
    try:
        # Validate email address
        if not validate_email(email_address):
            safe_print(f"Invalid email address format: {email_address}")
            return False, f"Invalid email address format: {email_address}"

        # URL encode all parameters to handle special characters
        encoded_email = urllib.parse.quote(email_address)
        encoded_subject = urllib.parse.quote(subject) if subject else ""
        encoded_body = urllib.parse.quote(body) if body else ""

        # Create Gmail compose URL with pre-filled recipient, subject, and body
        gmail_compose_url = f"https://mail.google.com/mail/?view=cm&fs=1&to={encoded_email}"

        if encoded_subject:
            gmail_compose_url += f"&su={encoded_subject}"

        if encoded_body:
            gmail_compose_url += f"&body={encoded_body}"

        safe_print(f"Opening Gmail compose window for: {email_address}")
        if subject:
            safe_print(f"Subject: {subject}")
        if body:
            safe_print(f"Body preview: {body[:50]}...")

        # Check if automatic sending is enabled
        if AUTO_SEND_EMAILS:
            # First, open Gmail compose window for user to review
            safe_print("Opening Gmail compose window for review...")
            webbrowser.open(gmail_compose_url)

            # Ask for voice confirmation before sending
            safe_print("Requesting voice confirmation before sending...")
            confirmation_result = _get_voice_confirmation(email_address, subject, body)

            if confirmation_result:
                # User confirmed, proceed with automation
                safe_print("User confirmed. Attempting to send email automatically...")

                # Try automation with timeout
                try:
                    import threading
                    import time

                    automation_result = [False]  # Use list to allow modification in thread

                    def run_automation():
                        try:
                            result = _automate_gmail_send_complete(email_address, subject, body)
                            automation_result[0] = result
                        except Exception as e:
                            safe_print(f"Automation thread error: {e}")
                            automation_result[0] = False

                    # Run automation in a separate thread with timeout
                    automation_thread = threading.Thread(target=run_automation, daemon=True)
                    automation_thread.start()
                    automation_thread.join(timeout=45)  # 45 second timeout

                    if automation_thread.is_alive():
                        safe_print("Automation timed out after 45 seconds, falling back to manual...")
                        success = False
                    else:
                        success = automation_result[0]

                except Exception as e:
                    safe_print(f"Error in automation thread: {e}")
                    success = False

                if success:
                    return True, f"Email sent successfully to {email_address}"
                else:
                    return True, f"Gmail compose window is open. Please click Send to complete sending to {email_address}."
            else:
                # User declined or confirmation failed
                return True, f"Gmail compose window opened for {email_address}. You can review and send manually."
        else:
            # Open Gmail compose window for manual sending
            safe_print("Automatic email sending is disabled. Opening compose window...")
            webbrowser.open(gmail_compose_url)
            return True, f"Gmail compose window opened for {email_address}. Please click Send to complete."

    except Exception as e:
        safe_print(f"Error with Gmail automation: {e}")
        return False, f"Error with Gmail: {str(e)}"

def System(command):
    if not HAS_KEYBOARD or not keyboard:
        safe_print(f"Keyboard module not available - cannot execute system command: {command}")
        return False

    def mute():
        keyboard.press_and_release("volume mute")

    def unmute():
        keyboard.press_and_release("volume mute")

    def volume_up():
        keyboard.press_and_release("volume up")

    def volume_down():
        keyboard.press_and_release("volume down")

    if command == "mute":
        mute()
    elif command == "unmute":
        unmute()
    elif command == "volume up":
        volume_up()
    elif command == "volume down":
        volume_down()

    return True

async def TranslateAndExecute(commands: list[str]):
    funcs = []

    for command in commands:
        if command.startswith("open "):
            if "open it" in command or "open file" == command:
                pass
            else:
                # Check if it's a direct URL (starts with http:// or https://)
                app_or_url = command.removeprefix("open ")
                if app_or_url.startswith(('http://', 'https://')):
                    fun = asyncio.to_thread(OpenURL, app_or_url)
                else:
                    fun = asyncio.to_thread(OpenApp, app_or_url)
                funcs.append(fun)
        elif command.startswith("url "):
            # Direct URL command
            url = command.removeprefix("url ")
            # Store the URL and command index for later processing
            fun = asyncio.to_thread(OpenURL, url)
            funcs.append(fun)
        elif command.startswith("general "):
            pass
        elif command.startswith("realtime "):
            pass
        elif command.startswith("close "):
            fun = asyncio.to_thread(CloseApp, command.removeprefix("close "))
            funcs.append(fun)
        elif command.startswith("play "):
            fun = asyncio.to_thread(PlayYoutube, command.removeprefix("play "))
            funcs.append(fun)
        elif command.startswith("content "):
            fun = asyncio.to_thread(Content, command.removeprefix("content "))
            funcs.append(fun)
        elif command.startswith("google search "):
            fun = asyncio.to_thread(GoogleSearch, command.removeprefix("google search"))
            funcs.append(fun)
        elif command.startswith("youtube search "):
            fun = asyncio.to_thread(YoutubeSearch, command.removeprefix("youtube search"))
            funcs.append(fun)
        elif command.startswith("system "):
            fun = asyncio.to_thread(System, command.removeprefix("system "))
            funcs.append(fun)
        elif command.startswith("send email "):
            email_address = command.removeprefix("send email ")
            fun = asyncio.to_thread(SendEmail, email_address)
            funcs.append(fun)
        else:
            safe_print(f"No Function Found. For {command}")

    results = await asyncio.gather(*funcs)

    website_names = []
    success_statuses = []
    email_messages = []

    for result in results:
        if isinstance(result, tuple) and len(result) == 2:
            # This is from OpenURL or SendEmail function
            success, message = result
            success_statuses.append(success)
            if success:
                if "Gmail" in message:
                    email_messages.append(message)
                else:
                    website_names.append(message)
        elif isinstance(result, str):
            yield result
        else:
            success_statuses.append(result)

    # Return website names and email messages for speech
    if website_names:
        yield {"website_names": website_names, "success": all(success_statuses)}

    if email_messages:
        yield {"email_messages": email_messages, "success": all(success_statuses)}


async def Automation(commands: list[str]):
    website_info = None

    async for result in TranslateAndExecute(commands):
        if isinstance(result, dict) and "website_names" in result:
            website_info = result

    # If we have website names, return them for speech
    if website_info and website_info["success"]:
        website_names = website_info["website_names"]
        if len(website_names) == 1:
            return f"Opening {website_names[0]}"
        elif len(website_names) > 1:
            websites_text = ", ".join(website_names[:-1]) + " and " + website_names[-1]
            return f"Opening {websites_text}"

    return True
